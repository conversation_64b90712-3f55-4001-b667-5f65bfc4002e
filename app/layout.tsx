import { <PERSON><PERSON><PERSON> } from 'next/font/google'
import './globals.css'
import type { Metadata } from 'next'

const rubik = Rubik({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-rubik',
  weight: ['300', '400', '500', '600', '700', '800', '900'],
})

export const metadata: Metadata = {
  title: 'Graphyn - Decentralized Trading',
  description: 'Next-generation platform for decentralized trading',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={`${rubik.variable} font-rubik antialiased`}>
        {children}
      </body>
    </html>
  )
}
