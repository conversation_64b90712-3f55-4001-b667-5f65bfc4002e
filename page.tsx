"use client"

import Header from "./components/Header"
import { useEffect, useState } from "react"
import { AppSidebar } from "./components/app-sidebar"
import { SidebarProvider, useSidebar } from "./components/app-sidebar/sidebar-context"

/**
 * Main home page component
 * 
 * @returns JSX.Element
 */
export default function Home() {
  const [isMounted, setIsMounted] = useState(false)
  
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // If not mounted, render nothing to avoid hydration issues
  if (!isMounted) return null

  return (
    <SidebarProvider>
      <div className="flex min-h-screen bg-[#0a0a0c] text-white">
        <AppSidebar />
        <SidebarAwareContent />
      </div>
    </SidebarProvider>
  )
}

/**
 * Content component that adjusts its layout based on sidebar state
 * 
 * @returns JSX.Element
 */
function SidebarAwareContent() {
  const { isExpanded } = useSidebar()
  
  return (
    <div className={`flex-1 transition-all duration-300 ${isExpanded ? 'ml-[240px]' : 'ml-[60px]'}`}>
      <Header />
      <main className="container mx-auto px-4 pt-24 pb-16">
        <div className="flex flex-col items-center justify-center min-h-[80vh]">
          <h1 className="text-4xl md:text-6xl font-bold text-center mb-6 text-[#F9D749]">
            GRAPHYN
          </h1>
          <p className="text-xl md:text-2xl text-center max-w-3xl">
            Welcome to the future of decentralized trading
          </p>
        </div>
      </main>
    </div>
  )
}
