"use client"

import Header from "./components/Header"
import { useEffect, useState } from "react"

/**
 * Main home page component
 *
 * @returns JSX.Element
 */
export default function Home() {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  // If not mounted, render nothing to avoid hydration issues
  if (!isMounted) return null

  return (
    <div className="flex min-h-screen bg-[#0f0f11] text-white">
      <div className="flex-1">
        <Header />
        <main className="mx-[2.5rem] pt-28 pb-16">
          <div className="flex flex-col items-center justify-center min-h-[80vh]">
            <h1 className="text-4xl md:text-6xl font-bold text-center mb-6 text-[#F9D749]">
              GRAPHYN
            </h1>
            <p className="text-xl md:text-2xl text-center max-w-3xl">
              Welcome to the future of decentralized trading
            </p>
          </div>
        </main>
      </div>
    </div>
  )
}
