import { Space_Grotesk } from "next/font/google"
import { JetBrains_Mono } from "next/font/google"
import { <PERSON><PERSON><PERSON> } from "next/font/google"
import type React from "react"
import type { Metadata } from "next"

const spaceGrotesk = Space_Grotesk({
  subsets: ["latin"],
  variable: "--font-space-grotesk",
})

const jetBrainsMono = JetBrains_Mono({
  subsets: ["latin"],
  variable: "--font-mono",
})

const rubik = Rubik({ 
  subsets: ['latin'],
  variable: '--font-rubik', 
  weight: ['300', '400', '500', '700'],
  display: 'swap'
});

export const metadata: Metadata = {
  title: {
    default: "Graphyn | Contextual Intelligence Layer",
    template: "%s | Graphyn",
  },
  description:
    "Graphyn provides a powerful SDK for seamless integration of personalized, context-aware features in your applications.",
  keywords: [
    "contextual intelligence",
    "AI",
    "machine learning",
    "personalization",
    "SDK",
    "developer tools",
    "context-aware",
    "user experience",
  ],
  authors: [{ name: "<PERSON><PERSON><PERSON>" }],
  creator: "<PERSON>raphy<PERSON>",
  publisher: "<PERSON>raphy<PERSON>",
  robots: {
    index: true,
    follow: true,
  },
  icons: {
    icon: [
      {
        url: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Group%201171274909-6qAc0l9yFtnmWK0yfTDOWwiPgp4bEd.png",
        sizes: "32x32",
        type: "image/png",
      },
      {
        url: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Group%201171274909-6qAc0l9yFtnmWK0yfTDOWwiPgp4bEd.png",
        sizes: "192x192",
        type: "image/png",
      },
    ],
    shortcut:
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Group%201171274909-6qAc0l9yFtnmWK0yfTDOWwiPgp4bEd.png",
    apple:
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Group%201171274909-6qAc0l9yFtnmWK0yfTDOWwiPgp4bEd.png",
  },
  manifest: "/site.webmanifest",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://graphyn.xyz",
    siteName: "Graphyn",
    title: "Graphyn | Contextual Intelligence Layer",
    description:
      "Graphyn provides a powerful SDK for seamless integration of personalized, context-aware features in your applications.",
    images: [
      {
        url: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Group%201171274909-6qAc0l9yFtnmWK0yfTDOWwiPgp4bEd.png",
        width: 1200,
        height: 630,
        alt: "Graphyn Logo",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Graphyn | Contextual Intelligence Layer",
    description:
      "Graphyn provides a powerful SDK for seamless integration of personalized, context-aware features in your applications.",
    images: [
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Group%201171274909-6qAc0l9yFtnmWK0yfTDOWwiPgp4bEd.png",
    ],
    creator: "@graphyn",
    site: "@graphyn",
  },
  viewport: {
    width: "device-width",
    initialScale: 1,
    maximumScale: 5,
  },
  applicationName: "Graphyn",
  appleWebApp: {
    capable: true,
    title: "Graphyn",
    statusBarStyle: "black-translucent",
  },
  formatDetection: {
    telephone: false,
  },
  themeColor: "#924FE8",
  category: "technology",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${spaceGrotesk.variable} ${jetBrainsMono.variable} ${rubik.variable} font-sans bg-[#0B0C0E]`}>{children}</body>
    </html>
  )
}
