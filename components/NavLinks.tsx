"use client"

import Link from "next/link"

/**
 * Navigation links component for the header
 * 
 * @returns JSX.Element
 */
export default function NavLinks() {
  return (
    <div className="flex flex-row items-center gap-6">
      <Link href="/platform" className="font-rubik font-normal text-[18px] leading-[21px] uppercase text-white hover:text-[#F9D749] transition-colors">
        Platform
      </Link>
      <Link href="/learn" className="font-rubik font-normal text-[18px] leading-[21px] uppercase text-white hover:text-[#F9D749] transition-colors">
        Learn
      </Link>
      <Link href="/about" className="font-rubik font-normal text-[18px] leading-[21px] uppercase text-white hover:text-[#F9D749] transition-colors">
        About
      </Link>
    </div>
  );
} 