"use client"

import { useState } from "react"

/**
 * Modal component for resetting password with a token link
 * 
 * @param props Component properties
 * @param props.isOpen Whether the modal is open
 * @param props.onClose Function to close the modal
 * @param props.token Reset token from the URL (optional)
 * @returns JSX.Element
 */
export default function ResetPasswordModal({ 
  isOpen, 
  onClose,
  token
}: { 
  isOpen: boolean;
  onClose: () => void;
  token?: string;
}) {
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");
  
  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (password.length < 8) {
      setPasswordError("Password must be at least 8 characters long");
      return;
    }
    
    if (password !== confirmPassword) {
      setPasswordError("Passwords do not match");
      return;
    }
    
    // Reset password logic would go here
    // Using the token from props and the new password
    
    // Clear errors and close modal
    setPasswordError("");
    onClose();
  };

  return (
    <div className="fixed inset-0 flex items-start justify-center z-50 pt-[15vh]">
      <div 
        className="fixed inset-0 bg-black/50" 
        onClick={onClose}
        onKeyDown={(e) => e.key === 'Escape' && onClose()}
        role="button"
        tabIndex={0}
        aria-label="Close modal"
      />
      <div className="relative bg-[#18181a] rounded-lg overflow-hidden w-[420px] max-w-full">
        {/* Modal Header */}
        <div className="flex bg-[#121214] border-b border-[#242426] py-3 px-6">
          <h2 className="text-[#f5f5f5] font-rubik font-medium text-[18px]">
            Reset Password
          </h2>
          <button 
            type="button" 
            onClick={onClose}
            className="absolute right-4 top-3 text-[#9293a0] hover:text-[#f5f5f5]"
            aria-label="Close modal"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true">
              <line x1="18" y1="6" x2="6" y2="18" />
              <line x1="6" y1="6" x2="18" y2="18" />
            </svg>
          </button>
        </div>
        
        {/* Form Content */}
        <div className="p-6">
          <div className="mb-4 text-[#d0d0da] font-rubik">
            Enter your new password below.
          </div>
          
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="new-password" className="block text-[#d0d0da] text-sm font-rubik mb-1 uppercase font-medium">
                NEW PASSWORD
              </label>
              <input
                id="new-password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-2 bg-[#0a0a0c] text-[#d0d0da] font-rubik rounded-md focus:outline-none focus:ring-1 focus:ring-[#F9D749]"
                placeholder="••••••••"
                required
              />
            </div>
            
            <div className="mb-4">
              <label htmlFor="confirm-password" className="block text-[#d0d0da] text-sm font-rubik mb-1 uppercase font-medium">
                CONFIRM PASSWORD
              </label>
              <input
                id="confirm-password"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="w-full px-4 py-2 bg-[#0a0a0c] text-[#d0d0da] font-rubik rounded-md focus:outline-none focus:ring-1 focus:ring-[#F9D749]"
                placeholder="••••••••"
                required
              />
              
              {passwordError && (
                <p className="mt-1 text-red-400 text-sm font-rubik">{passwordError}</p>
              )}
            </div>
            
            <div className="mt-6">
              <button
                type="submit"
                className="w-full py-2 bg-[#F9D749] text-[#171923] font-rubik font-medium rounded-md hover:bg-[#fae47a] transition-colors"
              >
                Reset Password
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
} 