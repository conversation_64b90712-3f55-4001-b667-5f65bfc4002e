"use client"

import { useState } from "react"
import AuthModal from "./AuthModal"
import SteppedRegistrationModal from "./SteppedRegistrationModal"

/**
 * Header authentication buttons component with login and register functionality
 * 
 * @returns JSX.Element
 */
export default function HeaderAuthButtons() {
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showRegisterModal, setShowRegisterModal] = useState(false);
  const [authView, setAuthView] = useState<"signin" | "signup">("signin");

  const handleLoginClick = () => {
    setAuthView("signin");
    setShowAuthModal(true);
  };

  const handleRegisterClick = () => {
    setShowRegisterModal(true);
  };

  return (
    <div className="flex items-center space-x-4">
      <button
        type="button"
        onClick={handleLoginClick}
        className="font-rubik font-medium text-[16px] text-white hover:text-[#F9D749] transition-colors"
      >
        Login
      </button>
      <button
        type="button"
        onClick={handleRegisterClick}
        className="font-rubik font-medium text-[16px] bg-[#F9D749] text-[#171923] py-1.5 px-4 rounded-md hover:bg-[#fae47a] transition-colors"
      >
        Register
      </button>

      {/* Modals */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        initialView={authView}
      />
      <SteppedRegistrationModal
        isOpen={showRegisterModal}
        onClose={() => setShowRegisterModal(false)}
      />
    </div>
  );
} 