"use client"

import { useState, useRef, useEffect } from "react"

/**
 * Wallet display component showing balance and wallet connection button
 * 
 * @param props Component properties
 * @param props.balance Current wallet balance
 * @param props.onConnectClick Handler for wallet connect button click
 * @returns JSX.Element
 */
export default function WalletDisplay({ 
  balance = "0.00000000", 
  onConnectClick 
}: { 
  balance?: string;
  onConnectClick?: () => void;
}) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const handleWalletClick = () => {
    if (onConnectClick) {
      onConnectClick();
    }
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="flex items-center">
      <div className="relative" ref={dropdownRef}>
        <button
          ref={buttonRef}
          type="button"
          onClick={toggleDropdown}
          className="flex items-center bg-[#020203] border border-[#242426] rounded-md py-1.5 px-3 mr-2 cursor-pointer"
        >
          <span className="text-white font-rubik font-medium">{balance}</span>
          <div className="ml-1.5 mr-1">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
              <circle cx="8" cy="8" r="8" fill="#F9D749"/>
              <path d="M8 3.5L8 12.5" stroke="#000" strokeWidth="1.5" strokeLinecap="round"/>
              <path d="M11.5 7L4.5 7" stroke="#000" strokeWidth="1.5" strokeLinecap="round"/>
            </svg>
          </div>
          <svg 
            width="14" 
            height="14" 
            viewBox="0 0 24 24" 
            fill="none" 
            xmlns="http://www.w3.org/2000/svg"
            aria-hidden="true"
            className={`transform ${isDropdownOpen ? 'rotate-180' : ''} transition-transform duration-200`}
          >
            <path 
              d="M7 10l5 5 5-5" 
              stroke="white" 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round"
            />
          </svg>
        </button>

        {/* Dropdown Menu */}
        {isDropdownOpen && (
          <div 
            style={{ width: `${buttonRef.current?.offsetWidth}px` }}
            className="absolute top-0 left-0 bg-[#020203] border border-[#242426] rounded-md z-50 overflow-hidden"
          >
            <div className="py-1.5 px-3 border-b border-[#242426] flex items-center">
              <span className="text-white font-rubik font-medium">0.00000000</span>
              <div className="ml-1.5">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                  <circle cx="8" cy="8" r="8" fill="#F9D749"/>
                  <path d="M8 3.5L8 12.5" stroke="#000" strokeWidth="1.5" strokeLinecap="round"/>
                  <path d="M11.5 7L4.5 7" stroke="#000" strokeWidth="1.5" strokeLinecap="round"/>
                </svg>
              </div>
            </div>
            <div className="py-1.5 px-3 border-b border-[#242426] flex items-center">
              <span className="text-white font-rubik font-medium">0.00000000</span>
              <div className="ml-1.5">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                  <circle cx="8" cy="8" r="8" fill="#F9D749"/>
                  <path d="M8 3.5L8 12.5" stroke="#000" strokeWidth="1.5" strokeLinecap="round"/>
                  <path d="M11.5 7L4.5 7" stroke="#000" strokeWidth="1.5" strokeLinecap="round"/>
                </svg>
              </div>
            </div>
            <div className="py-1.5 px-3 border-b border-[#242426] flex items-center">
              <span className="text-white font-rubik font-medium">0.00000000</span>
              <div className="ml-1.5">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                  <circle cx="8" cy="8" r="8" fill="#F9D749"/>
                  <path d="M8 3.5L8 12.5" stroke="#000" strokeWidth="1.5" strokeLinecap="round"/>
                  <path d="M11.5 7L4.5 7" stroke="#000" strokeWidth="1.5" strokeLinecap="round"/>
                </svg>
              </div>
            </div>
            <div className="py-1.5 px-3 flex items-center">
              <span className="text-white font-rubik font-medium">0.00000000</span>
              <div className="ml-1.5">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                  <circle cx="8" cy="8" r="8" fill="#F9D749"/>
                  <path d="M8 3.5L8 12.5" stroke="#000" strokeWidth="1.5" strokeLinecap="round"/>
                  <path d="M11.5 7L4.5 7" stroke="#000" strokeWidth="1.5" strokeLinecap="round"/>
                </svg>
              </div>
            </div>
          </div>
        )}
      </div>
      <button 
        type="button"
        onClick={handleWalletClick}
        className="bg-[#F9D749] text-[#171923] font-rubik font-medium rounded-md py-1.5 px-4 hover:bg-[#fae47a] transition-colors"
      >
        <span className="uppercase">WALLET</span>
      </button>
    </div>
  );
} 