"use client"

import Link from "next/link"

/**
 * Main application header with navigation, authentication, and wallet controls
 *
 * @returns JSX.Element
 */
export default function Header() {
  return (
    <header className="fixed top-0 left-0 right-0 bg-[#0a0a0c] z-40">
      {/* Main Navigation Bar */}
      <div className="h-[58px] border-b border-[#242426]">
        <div className="mx-[2.5rem] h-full flex items-center justify-between">
          {/* Logo and Navigation Links */}
          <div className="flex items-center">
            <Link href="/" className="font-rubik font-bold text-2xl text-[#F9D749] mr-8">
              GRAPHYN
            </Link>

            {/* Navigation Links */}
            <nav className="hidden md:flex items-center space-x-6">
              <Link href="/trades" className="font-rubik font-medium text-[14px] text-[#9293a0] hover:text-[#F9D749] uppercase tracking-wide">
                TRADES
              </Link>
              <Link href="/markets" className="font-rubik font-medium text-[14px] text-[#9293a0] hover:text-[#F9D749] uppercase tracking-wide">
                MARKETS
              </Link>
              <Link href="/earn" className="font-rubik font-medium text-[14px] text-[#9293a0] hover:text-[#F9D749] uppercase tracking-wide">
                EARN
              </Link>
              <Link href="/nft" className="font-rubik font-medium text-[14px] text-[#9293a0] hover:text-[#F9D749] uppercase tracking-wide">
                NFT
              </Link>
            </nav>
          </div>

          {/* Center Wallet Display */}
          <div className="flex items-center space-x-6 text-[13px] font-rubik">
            <div className="flex items-center space-x-1">
              <span className="text-[#9293a0]">Vault Total</span>
              <span className="text-white font-medium">$1.81B</span>
            </div>
            <div className="flex items-center space-x-1">
              <span className="text-[#9293a0]">24H Wagered</span>
              <span className="text-white font-medium">$113.6M</span>
            </div>
            <div className="flex items-center space-x-1">
              <span className="text-[#9293a0]">Top Case or Win Rate %</span>
              <span className="text-[#2BDD02] font-medium">10.29%</span>
            </div>
          </div>

          {/* Right Side Buttons */}
          <div className="flex items-center space-x-3">
            <button
              type="button"
              className="font-rubik font-medium text-[14px] text-[#9293a0] hover:text-white transition-colors"
            >
              Login
            </button>
            <button
              type="button"
              className="font-rubik font-medium text-[14px] bg-[#F9D749] text-[#171923] py-2 px-4 rounded-md hover:bg-[#fae47a] transition-colors uppercase"
            >
              ACCESS
            </button>
            <button
              type="button"
              className="font-rubik font-medium text-[14px] bg-[#2BDD02] text-white py-2 px-4 rounded-md hover:bg-[#25c102] transition-colors uppercase"
            >
              CONNECT WALLET
            </button>
            <button
              type="button"
              className="p-2 text-[#9293a0] hover:text-white transition-colors"
              aria-label="Toggle theme"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="5"/>
                <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
              </svg>
            </button>
            <button
              type="button"
              className="p-2 text-[#9293a0] hover:text-white transition-colors"
              aria-label="Chat"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Sub Navigation Bar */}
      <div className="h-[42px] bg-[#121214] border-b border-[#242426]">
        <div className="mx-[2.5rem] h-full flex items-center">
          <nav className="flex items-center space-x-6">
            <Link
              href="/overview"
              className="font-rubik font-medium text-[14px] text-[#9293a0] hover:text-[#F9D749] transition-colors uppercase tracking-wide"
            >
              QUICKSWAP
            </Link>
            <Link
              href="/portfolio"
              className="font-rubik font-medium text-[14px] text-[#9293a0] hover:text-[#F9D749] transition-colors uppercase tracking-wide"
            >
              POOLS
            </Link>
            <Link
              href="/analytics"
              className="font-rubik font-medium text-[14px] text-[#9293a0] hover:text-[#F9D749] transition-colors uppercase tracking-wide"
            >
              POOL CREATION
            </Link>
            <Link
              href="/settings"
              className="font-rubik font-medium text-[14px] text-[#9293a0] hover:text-[#F9D749] transition-colors uppercase tracking-wide"
            >
              DASHBOARD
            </Link>
          </nav>
        </div>
      </div>
    </header>
  );
}