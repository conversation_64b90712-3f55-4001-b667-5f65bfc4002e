"use client"

import Link from "next/link"
import HeaderAuthButtons from "./HeaderAuthButtons"
import WalletDisplay from "./WalletDisplay"
import { useState } from "react"

/**
 * Main application header with navigation, authentication, and wallet controls
 * 
 * @returns JSX.Element
 */
export default function Header() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  return (
    <header className="fixed top-0 left-0 right-0 bg-[#0a0a0c] border-b border-[#242426] z-40">
      <div className="container mx-auto px-4 py-3 flex items-center justify-between">
        {/* Logo */}
        <Link href="/" className="font-rubik font-bold text-2xl text-[#F9D749]">
          GRAPHYN
        </Link>
        
        {/* Navigation Links */}
        <nav className="hidden md:flex items-center space-x-8 mx-8">
          <Link href="/trades" className="font-rubik font-medium text-[16px] text-white hover:text-[#F9D749] uppercase">
            Trades
          </Link>
          <Link href="/markets" className="font-rubik font-medium text-[16px] text-white hover:text-[#F9D749] uppercase">
            Markets
          </Link>
          <Link href="/earn" className="font-rubik font-medium text-[16px] text-white hover:text-[#F9D749] uppercase">
            Earn
          </Link>
          <Link href="/nft" className="font-rubik font-medium text-[16px] text-white hover:text-[#F9D749] uppercase">
            NFT
          </Link>
        </nav>
        
        {/* Auth/Wallet Buttons */}
        <div className="flex items-center space-x-4">
          {isLoggedIn ? (
            <WalletDisplay 
              balance="0.00000000"
              onConnectClick={() => console.log("Connect wallet clicked")}
            />
          ) : (
            <HeaderAuthButtons />
          )}
        </div>
      </div>
    </header>
  );
} 