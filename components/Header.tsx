"use client"

import Link from "next/link"
import HeaderAuthButtons from "./HeaderAuthButtons"
import WalletDisplay from "./WalletDisplay"
import { useState } from "react"

/**
 * Main application header with navigation, authentication, and wallet controls
 *
 * @returns JSX.Element
 */
export default function Header() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  return (
    <header className="fixed top-0 left-0 right-0 bg-[#0a0a0c] z-40">
      {/* Main Navigation Bar */}
      <div className="h-[58px] border-b border-[#242426]">
        <div className="mx-[2rem] h-full flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="font-rubik font-bold text-2xl text-[#F9D749]">
            GRAPHYN
          </Link>

          {/* Navigation Links */}
          <nav className="hidden md:flex items-center space-x-8 ml-[0.75rem]">
            <Link href="/trades" className="font-rubik font-medium text-[16px] text-white hover:text-[#F9D749] uppercase">
              Trades
            </Link>
            <Link href="/markets" className="font-rubik font-medium text-[16px] text-white hover:text-[#F9D749] uppercase">
              Markets
            </Link>
            <Link href="/earn" className="font-rubik font-medium text-[16px] text-white hover:text-[#F9D749] uppercase">
              Earn
            </Link>
            <Link href="/nft" className="font-rubik font-medium text-[16px] text-white hover:text-[#F9D749] uppercase">
              NFT
            </Link>
          </nav>

          {/* Auth/Wallet Buttons */}
          <div className="flex items-center space-x-4">
            {isLoggedIn ? (
              <WalletDisplay
                balance="0.00000000"
                onConnectClick={() => console.log("Connect wallet clicked")}
              />
            ) : (
              <HeaderAuthButtons />
            )}
          </div>
        </div>
      </div>

      {/* Sub Navigation Bar */}
      <div className="h-[42px] bg-[#121214] border-b border-[#242426]">
        <div className="mx-[2rem] h-full flex items-center">
          <nav className="flex items-center space-x-6">
            <Link href="/overview" className="font-rubik font-medium text-[14px] text-[#9293a0] hover:text-white transition-colors">
              Overview
            </Link>
            <Link href="/portfolio" className="font-rubik font-medium text-[14px] text-[#9293a0] hover:text-white transition-colors">
              Portfolio
            </Link>
            <Link href="/analytics" className="font-rubik font-medium text-[14px] text-[#9293a0] hover:text-white transition-colors">
              Analytics
            </Link>
            <Link href="/settings" className="font-rubik font-medium text-[14px] text-[#9293a0] hover:text-white transition-colors">
              Settings
            </Link>
          </nav>
        </div>
      </div>
    </header>
  );
}