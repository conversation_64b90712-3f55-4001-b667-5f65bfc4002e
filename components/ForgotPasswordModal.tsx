"use client"

/**
 * Modal component for initiating password recovery
 * 
 * @param props Component properties
 * @param props.isOpen Whether the modal is open
 * @param props.onClose Function to close the modal
 * @returns JSX.Element
 */
export default function ForgotPasswordModal({ 
  isOpen, 
  onClose
}: { 
  isOpen: boolean;
  onClose: () => void;
}) {
  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Add password reset request logic here
    
    // For demo purposes, we'll just close the modal
    onClose();
  };

  return (
    <div className="fixed inset-0 flex items-start justify-center z-50 pt-[15vh]">
      <div 
        className="fixed inset-0 bg-black/50" 
        onClick={onClose}
        onKeyDown={(e) => e.key === 'Escape' && onClose()}
        role="button"
        tabIndex={0}
        aria-label="Close modal"
      />
      <div className="relative bg-[#18181a] rounded-lg overflow-hidden w-[420px] max-w-full">
        {/* Modal Header */}
        <div className="flex bg-[#121214] border-b border-[#242426] py-3 px-6">
          <h2 className="text-[#f5f5f5] font-rubik font-medium text-[18px]">
            Forgot Password
          </h2>
          <button 
            type="button" 
            onClick={onClose}
            className="absolute right-4 top-3 text-[#9293a0] hover:text-[#f5f5f5]"
            aria-label="Close modal"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true">
              <line x1="18" y1="6" x2="6" y2="18" />
              <line x1="6" y1="6" x2="18" y2="18" />
            </svg>
          </button>
        </div>
        
        {/* Form Content */}
        <div className="p-6">
          <div className="mb-4 text-[#d0d0da] font-rubik">
            Enter your email address and we'll send you a link to reset your password.
          </div>
          
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="reset-email" className="block text-[#d0d0da] text-sm font-rubik mb-1 uppercase font-medium">
                EMAIL
              </label>
              <input
                id="reset-email"
                type="email"
                className="w-full px-4 py-2 bg-[#0a0a0c] text-[#d0d0da] font-rubik rounded-md focus:outline-none focus:ring-1 focus:ring-[#F9D749]"
                placeholder="<EMAIL>"
                required
              />
            </div>
            
            <div className="mt-6">
              <button
                type="submit"
                className="w-full py-2 bg-[#F9D749] text-[#171923] font-rubik font-medium rounded-md hover:bg-[#fae47a] transition-colors"
              >
                Send Reset Link
              </button>
            </div>
            
            <div className="mt-4 text-center">
              <button 
                type="button" 
                className="text-[#F9D749] hover:underline font-rubik text-sm"
                onClick={onClose}
              >
                Back to Sign In
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
} 