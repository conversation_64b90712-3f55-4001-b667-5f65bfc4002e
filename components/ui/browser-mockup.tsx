"use client"

import React from "react"
import { cn } from "@/lib/utils"
import { X } from "lucide-react"

export interface BrowserMockupProps extends React.HTMLAttributes<HTMLDivElement> {
  url?: string
  showControls?: boolean
  showTrafficLights?: boolean
  showCloseButton?: boolean
  onClose?: () => void
  headerContent?: React.ReactNode
  footerContent?: React.ReactNode
  variant?: "mac" | "windows" | "minimal"
}

/**
 * A component that renders a browser window mockup with customizable URL, controls, and content.
 *
 * @param props The props for the component.
 * @param props.url The URL to display in the address bar.
 * @param props.showControls Whether to show browser controls.
 * @param props.showTrafficLights Whether to show macOS-style traffic light buttons.
 * @param props.showCloseButton Whether to show a close button on the right.
 * @param props.onClose Function called when the close button is clicked.
 * @param props.headerContent Additional content to render in the header.
 * @param props.footerContent Content to render in the footer.
 * @param props.variant Visual style variant of the browser mockup.
 * @param props.className Additional classes to apply to the container.
 * @param props.children The content to render inside the browser window.
 * @returns JSX.Element
 */
export function BrowserMockup({
  url = "graphyn.xyz",
  showControls = true,
  showTrafficLights = true,
  showCloseButton = false,
  onClose,
  headerContent,
  footerContent,
  variant = "mac",
  className,
  children,
  ...props
}: BrowserMockupProps) {
  // Use React.createElement to ensure React is used as a value
  const containerRef = React.useRef<HTMLDivElement>(null);
  
  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onClose) onClose();
  };
  
  return (
    <div
      ref={containerRef}
      className={cn(
        "rounded-xl overflow-hidden bg-card shadow-2xl border border-border",
        className
      )}
      {...props}
    >
      {/* Browser Controls */}
      <div className="flex items-center gap-2 px-4 py-3 bg-background/60 border-b border-border">
        {showControls && showTrafficLights && (
          <div className="flex gap-1.5">
            <button 
              className="w-3 h-3 rounded-[0.5px] bg-destructive hover:bg-destructive/90 cursor-pointer" 
              onClick={onClose}
              onKeyDown={(e) => e.key === 'Enter' && onClose?.()}
              aria-label="Close browser window"
              type="button"
            />
            <button 
              className="w-3 h-3 rounded-[0.5px] bg-secondary hover:bg-secondary/90 cursor-pointer" 
              type="button"
              aria-label="Minimize browser window"
            />
            <button 
              className="w-3 h-3 rounded-[0.5px] bg-[oklch(0.67_0.16_58.32)] hover:bg-[oklch(0.67_0.16_58.32)]/90 cursor-pointer"
              type="button" 
              aria-label="Maximize browser window"
            />
          </div>
        )}
        <div className="flex-1 mx-4">
          <div className="bg-background rounded-md py-1.5 px-3 text-xs text-muted-foreground max-w-[300px] flex items-center gap-2">
            <div className="w-4 h-4 rounded-full bg-muted" />
            {url}
          </div>
        </div>
        {showCloseButton && (
          <button 
            onClick={handleClose} 
            className="flex items-center justify-center h-6 w-6 rounded-full hover:bg-muted/50"
            type="button"
            aria-label="Close browser window"
          >
            <X className="h-3.5 w-3.5 text-muted-foreground" />
          </button>
        )}
        {headerContent}
      </div>

      {/* Content Area */}
      <div className="w-full">{children}</div>
      
      {/* Footer (optional) */}
      {footerContent && (
        <div className="px-4 py-2 bg-background/60 border-t border-border">
          {footerContent}
        </div>
      )}
    </div>
  )
} 