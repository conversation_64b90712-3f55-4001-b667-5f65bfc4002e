"use client"

import { useState, useEffect } from "react"

/**
 * Multi-step registration modal component with language selection and account creation
 * 
 * @param props Component properties
 * @param props.isOpen Whether the modal is open
 * @param props.onClose Function to close the modal
 * @returns JSX.Element
 */
export default function SteppedRegistrationModal({
  isOpen,
  onClose
}: {
  isOpen: boolean;
  onClose: () => void;
}) {
  const [currentStep, setCurrentStep] = useState(1);
  const [language, setLanguage] = useState("English");
  const [passwordStrength, setPasswordStrength] = useState({
    length: false,
    uppercase: false,
    number: false
  });
  const [formData, setFormData] = useState({
    email: "",
    username: "",
    password: "",
    dateOfBirth: "",
    phone: false,
    code: false
  });

  const totalSteps = 2;
  
  // Reset when modal closes
  useEffect(() => {
    if (!isOpen) {
      setCurrentStep(1);
    }
  }, [isOpen]);
  
  // Check password strength
  const checkPasswordStrength = (password: string) => {
    setPasswordStrength({
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      number: /[0-9]/.test(password)
    });
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setFormData({...formData, password: newPassword});
    checkPasswordStrength(newPassword);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [id.replace('register-', '')]: type === 'checkbox' ? checked : value
    });
  };

  const handleNextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-start justify-center z-50 pt-[15vh]">
      <div 
        className="fixed inset-0 bg-black/50" 
        onClick={onClose}
        onKeyDown={(e) => e.key === 'Escape' && onClose()}
        role="button"
        tabIndex={0}
        aria-label="Close modal"
      />
      <div className="relative bg-[#18181a] rounded-lg overflow-hidden w-[500px] max-w-full">
        {/* Modal Header */}
        <div className="flex justify-between items-center bg-[#121214] border-b border-[#242426] py-3 px-6">
          <button 
            type="button"
            className={`text-[#f5f5f5] font-rubik font-medium ${currentStep === 1 ? 'invisible' : ''}`}
            onClick={handlePreviousStep}
            disabled={currentStep === 1}
            aria-label="Go back"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true">
              <path d="M19 12H5M12 19l-7-7 7-7" />
            </svg>
          </button>
          <span className="text-[#9293a0] font-rubik">
            Step {currentStep} / {totalSteps}
          </span>
          <button 
            type="button" 
            onClick={onClose}
            className="text-[#9293a0] hover:text-[#f5f5f5]"
            aria-label="Close modal"
          >
            Exit
          </button>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-[#121214] h-1">
          <div 
            className="bg-[#2BDD02] h-full transition-all duration-300" 
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          />
        </div>
        
        {/* Step 1: Language Selection */}
        {currentStep === 1 && (
          <div className="p-6">
            <h2 className="text-[#f5f5f5] font-rubik font-bold text-2xl mb-4">
              Select Your Preferred Language
            </h2>
            <p className="text-[#d0d0da] font-rubik mb-6">
              Stake is available is several languages. Feel free to personalise your language across our site from the options below.
            </p>
            
            <div className="mb-6">
              <div className="relative">
                <select
                  value={language}
                  onChange={(e) => setLanguage(e.target.value)}
                  className="w-full bg-[#0a0a0c] text-[#f5f5f5] border border-[#242426] rounded-md py-3 px-4 appearance-none focus:outline-none focus:ring-1 focus:ring-[#F9D749]"
                >
                  <option value="English">English</option>
                  <option value="Spanish">Spanish</option>
                  <option value="French">French</option>
                  <option value="German">German</option>
                  <option value="Italian">Italian</option>
                  <option value="Portuguese">Portuguese</option>
                  <option value="Japanese">Japanese</option>
                  <option value="Chinese">Chinese</option>
                </select>
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-[#9293a0]" aria-hidden="true">
                    <path d="M6 9l6 6 6-6" />
                  </svg>
                </div>
              </div>
            </div>
            
            <button
              type="button"
              onClick={handleNextStep}
              className="w-full py-3 bg-[#1D7AE7] text-white font-rubik font-medium rounded-md hover:bg-[#166DCF] transition-colors"
            >
              Confirm
            </button>

            <div className="mt-6 text-center">
              <p className="text-[#9293a0] font-rubik text-sm">
                Already have an account? <button type="button" className="text-[#F9D749] hover:underline" onClick={onClose}>Sign in</button>
              </p>
            </div>
          </div>
        )}
        
        {/* Step 2: Create Account */}
        {currentStep === 2 && (
          <div className="p-6">
            <h2 className="text-[#f5f5f5] font-rubik font-bold text-2xl mb-4">
              Create an Account
            </h2>
            
            <form onSubmit={(e) => e.preventDefault()}>
              {/* Email */}
              <div className="mb-4">
                <label htmlFor="register-email" className="block text-[#d0d0da] text-sm font-rubik mb-1 font-medium">
                  Email <span className="text-[#F9D749]">*</span>
                </label>
                <input
                  id="register-email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-[#0a0a0c] text-[#d0d0da] font-rubik rounded-md focus:outline-none focus:ring-1 focus:ring-[#F9D749] border border-[#242426]"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              
              {/* Username */}
              <div className="mb-4">
                <label htmlFor="register-username" className="block text-[#d0d0da] text-sm font-rubik mb-1 font-medium">
                  Username <span className="text-[#F9D749]">*</span>
                </label>
                <input
                  id="register-username"
                  type="text"
                  value={formData.username}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-[#0a0a0c] text-[#d0d0da] font-rubik rounded-md focus:outline-none focus:ring-1 focus:ring-[#F9D749] border border-[#242426]"
                  placeholder="Enter username"
                  required
                />
                <div className="text-[#9293a0] text-xs mt-1">
                  Your username must be 3-14 characters long.
                </div>
              </div>
              
              {/* Password */}
              <div className="mb-4">
                <label htmlFor="register-password" className="block text-[#d0d0da] text-sm font-rubik mb-1 font-medium">
                  Password <span className="text-[#F9D749]">*</span>
                </label>
                <div className="relative">
                  <input
                    id="register-password"
                    type="password"
                    value={formData.password}
                    onChange={handlePasswordChange}
                    className="w-full px-4 py-3 bg-[#0a0a0c] text-[#d0d0da] font-rubik rounded-md focus:outline-none focus:ring-1 focus:ring-[#F9D749] border border-[#242426]"
                    placeholder="••••••••"
                    required
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#9293a0] hover:text-white"
                    aria-label="Toggle password visibility"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true">
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                      <circle cx="12" cy="12" r="3" />
                    </svg>
                  </button>
                </div>
                
                {/* Password strength indicators */}
                <div className="grid grid-cols-3 gap-2 mt-3">
                  <div className={`flex items-center ${formData.password.length > 0 ? (passwordStrength.length ? 'text-[#2BDD02]' : 'text-[#9293a0]') : 'opacity-50 text-[#9293a0]'}`}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={`mr-1 ${passwordStrength.length ? 'text-[#2BDD02]' : ''}`} aria-hidden="true">
                      <path d="M20 6L9 17l-5-5" />
                    </svg>
                    <span className="text-xs">8+ characters</span>
                  </div>
                  <div className={`flex items-center ${formData.password.length > 0 ? (passwordStrength.uppercase ? 'text-[#2BDD02]' : 'text-[#9293a0]') : 'opacity-50 text-[#9293a0]'}`}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={`mr-1 ${passwordStrength.uppercase ? 'text-[#2BDD02]' : ''}`} aria-hidden="true">
                      <path d="M20 6L9 17l-5-5" />
                    </svg>
                    <span className="text-xs">Uppercase</span>
                  </div>
                  <div className={`flex items-center ${formData.password.length > 0 ? (passwordStrength.number ? 'text-[#2BDD02]' : 'text-[#9293a0]') : 'opacity-50 text-[#9293a0]'}`}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={`mr-1 ${passwordStrength.number ? 'text-[#2BDD02]' : ''}`} aria-hidden="true">
                      <path d="M20 6L9 17l-5-5" />
                    </svg>
                    <span className="text-xs">Number</span>
                  </div>
                </div>
              </div>
              
              {/* Date of Birth */}
              <div className="mb-4">
                <label htmlFor="register-dateOfBirth" className="block text-[#d0d0da] text-sm font-rubik mb-1 font-medium">
                  Date of Birth <span className="text-[#F9D749]">*</span>
                </label>
                <div className="relative">
                  <input
                    id="register-dateOfBirth"
                    type="text"
                    value={formData.dateOfBirth}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-[#0a0a0c] text-[#d0d0da] font-rubik rounded-md focus:outline-none focus:ring-1 focus:ring-[#F9D749] border border-[#242426]"
                    placeholder="mm/dd/yyyy"
                    required
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#9293a0]">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
                      <line x1="16" y1="2" x2="16" y2="6" />
                      <line x1="8" y1="2" x2="8" y2="6" />
                      <line x1="3" y1="10" x2="21" y2="10" />
                    </svg>
                  </div>
                </div>
              </div>
              
              {/* Optional Fields */}
              <div className="mb-4 space-y-2">
                <div className="flex items-center">
                  <input
                    id="register-phone"
                    type="checkbox"
                    checked={formData.phone}
                    onChange={handleInputChange}
                    className="w-4 h-4 border-[#242426] rounded focus:ring-[#F9D749] text-[#F9D749]"
                  />
                  <label htmlFor="register-phone" className="ml-2 text-[#d0d0da] text-sm font-rubik">
                    Phone (Optional)
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    id="register-code"
                    type="checkbox"
                    checked={formData.code}
                    onChange={handleInputChange}
                    className="w-4 h-4 border-[#242426] rounded focus:ring-[#F9D749] text-[#F9D749]"
                  />
                  <label htmlFor="register-code" className="ml-2 text-[#d0d0da] text-sm font-rubik">
                    Code (Optional)
                  </label>
                </div>
              </div>
              
              <button
                type="submit"
                className="w-full py-3 bg-[#1D7AE7] text-white font-rubik font-medium rounded-md hover:bg-[#166DCF] transition-colors mt-4"
              >
                Continue
              </button>
              
              <div className="flex items-center my-4">
                <div className="flex-grow border-t border-[#242426]" />
                <div className="px-4 text-[#9293a0] text-sm">OR</div>
                <div className="flex-grow border-t border-[#242426]" />
              </div>
              
              {/* Social Login Buttons */}
              <div className="flex justify-center space-x-3">
                <button
                  type="button"
                  className="w-12 h-12 flex items-center justify-center bg-[#0a0a0c] text-[#f5f5f5] rounded-md hover:bg-[#13131c] transition-colors border border-[#242426]"
                  aria-label="Sign up with Facebook"
                >
                  <svg width="20" height="20" fill="#1877F2" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                  </svg>
                </button>
                <button
                  type="button"
                  className="w-12 h-12 flex items-center justify-center bg-[#0a0a0c] text-[#f5f5f5] rounded-md hover:bg-[#13131c] transition-colors border border-[#242426]"
                  aria-label="Sign up with Google"
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="#EA4335" aria-hidden="true">
                    <path d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z" />
                  </svg>
                </button>
                <button
                  type="button"
                  className="w-12 h-12 flex items-center justify-center bg-[#0a0a0c] text-[#f5f5f5] rounded-md hover:bg-[#13131c] transition-colors border border-[#242426]"
                  aria-label="Sign up with Discord"
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="#5865F2" aria-hidden="true">
                    <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.39-.444.986-.608 1.43a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.43.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z" />
                  </svg>
                </button>
                <button
                  type="button"
                  className="w-12 h-12 flex items-center justify-center bg-[#0a0a0c] text-[#f5f5f5] rounded-md hover:bg-[#13131c] transition-colors border border-[#242426]"
                  aria-label="Sign up with Twitch"
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="#6441A4" aria-hidden="true">
                    <path d="M11.571 4.714h1.715v5.143H11.57zm4.715 0H18v5.143h-1.714zM6 0L1.714 4.286v15.428h5.143V24l4.286-4.286h3.428L22.286 12V0zm14.571 11.143l-3.428 3.428h-3.429l-3 3v-3H6.857V1.714h13.714Z" />
                  </svg>
                </button>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  );
} 