"use client"

/**
 * Modal component for connecting cryptocurrency wallets
 * 
 * @param props Component properties
 * @param props.isOpen Whether the modal is open
 * @param props.onClose Function to close the modal
 * @returns JSX.Element
 */
export default function WalletConnector({ 
  isOpen, 
  onClose
}: { 
  isOpen: boolean;
  onClose: () => void;
}) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-start justify-center z-50 pt-[15vh]">
      <div 
        className="fixed inset-0 bg-black/50" 
        onClick={onClose}
        onKeyDown={(e) => e.key === 'Escape' && onClose()}
        role="button"
        tabIndex={0}
        aria-label="Close modal"
      />
      <div className="relative bg-[#18181a] rounded-lg overflow-hidden w-[420px] max-w-full">
        {/* Modal Header */}
        <div className="flex bg-[#121214] border-b border-[#242426] py-3 px-6">
          <h2 className="text-[#f5f5f5] font-rubik font-medium text-[18px]">
            Connect Wallet
          </h2>
          <button 
            type="button" 
            onClick={onClose}
            className="absolute right-4 top-3 text-[#9293a0] hover:text-[#f5f5f5]"
            aria-label="Close modal"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true">
              <line x1="18" y1="6" x2="6" y2="18" />
              <line x1="6" y1="6" x2="18" y2="18" />
            </svg>
          </button>
        </div>
        
        {/* Wallet Options */}
        <div className="p-6">
          <div className="mb-4 text-[#d0d0da] font-rubik">
            Connect with one of the available wallet providers or create a new wallet.
          </div>
          
          <div className="space-y-3">
            <button 
              type="button"
              className="flex items-center justify-between w-full py-3 px-4 bg-[#0a0a0c] text-[#f5f5f5] font-rubik rounded-md hover:bg-[#13131c] transition-colors"
            >
              <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                    <path d="M20 8h-16c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2v-10c0-1.1-.9-2-2-2zm0 12h-16v-10h16v10zm-2-14h-12c-1.1 0-2 .9-2 2h16c0-1.1-.9-2-2-2zm-2-4h-8c-1.1 0-2 .9-2 2h12c0-1.1-.9-2-2-2z" fill="white"/>
                  </svg>
                </div>
                MetaMask
              </div>
              <span className="text-[#9293a0] text-sm">Popular</span>
            </button>
            
            <button 
              type="button"
              className="flex items-center justify-between w-full py-3 px-4 bg-[#0a0a0c] text-[#f5f5f5] font-rubik rounded-md hover:bg-[#13131c] transition-colors"
            >
              <div className="flex items-center">
                <div className="w-8 h-8 bg-[#3396ff] rounded-full flex items-center justify-center mr-3">
                  <svg width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                    <path d="M10 0C4.478 0 0 3.357 0 7.5C0 11.643 4.478 15 10 15C15.522 15 20 11.643 20 7.5C20 3.357 15.522 0 10 0ZM13.983 9.516C12.157 10.957 8.834 10.957 7 9.516V8.25C7 7.836 7.335 7.5 7.75 7.5H13.25C13.665 7.5 14 7.836 14 8.25V9.516H13.983Z" fill="white"/>
                  </svg>
                </div>
                WalletConnect
              </div>
            </button>
            
            <button 
              type="button"
              className="flex items-center justify-between w-full py-3 px-4 bg-[#0a0a0c] text-[#f5f5f5] font-rubik rounded-md hover:bg-[#13131c] transition-colors"
            >
              <div className="flex items-center">
                <div className="w-8 h-8 bg-[#0052ff] rounded-full flex items-center justify-center mr-3 text-white font-bold">
                  C
                </div>
                Coinbase Wallet
              </div>
            </button>
            
            <button 
              type="button"
              className="flex items-center justify-between w-full py-3 px-4 bg-[#0a0a0c] text-[#f5f5f5] font-rubik rounded-md hover:bg-[#13131c] transition-colors"
            >
              <div className="flex items-center">
                <div className="w-8 h-8 bg-[#F9D749] rounded-full flex items-center justify-center mr-3">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                    <path d="M12 14.5v2m0-6v1.5m0-4c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8z" stroke="black" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                More Options
              </div>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                <path d="M9 18l6-6-6-6" stroke="#9293a0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </div>
          
          <div className="mt-6">
            <button
              type="button"
              className="w-full py-2 bg-[#F9D749] text-[#171923] font-rubik font-medium rounded-md hover:bg-[#fae47a] transition-colors"
            >
              CREATE NEW WALLET
            </button>
          </div>
          
          <div className="mt-4 text-center">
            <button 
              type="button" 
              className="text-[#F9D749] hover:underline font-rubik text-sm"
              onClick={onClose}
            >
              What is a wallet?
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 