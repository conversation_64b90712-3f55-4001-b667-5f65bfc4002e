"use client"

import Link from 'next/link'
import { useState } from 'react'
import { useSidebar } from './SidebarContext'

type SidebarBaseButtonProps = {
  id: string
  icon: React.ReactNode
  label: string
  href: string
  isActive?: boolean
  onClick?: (event: React.MouseEvent<HTMLAnchorElement>) => void
}

/**
 * Base button component for sidebar navigation items
 * 
 * @param props Component properties
 * @param props.id Unique identifier for the button
 * @param props.icon Icon element to display
 * @param props.label Text label for the button
 * @param props.href Navigation target URL
 * @param props.isActive Whether this button is currently active
 * @param props.onClick Optional click handler
 * @returns JSX.Element
 */
export default function SidebarBaseButton({
  id,
  icon,
  label,
  href,
  isActive,
  onClick
}: SidebarBaseButtonProps) {
  const { isExpanded, setActiveId } = useSidebar()
  const [isHovered, setIsHovered] = useState(false)
  
  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    setActiveId(id)
    if (onClick) onClick(e)
  }
  
  return (
    <Link
      href={href}
      onClick={handleClick}
      className={`
        flex items-center h-[40px] transition-all duration-200 rounded-md
        ${isExpanded ? 'w-[220px]' : 'w-[40px] justify-center'}
        ${isActive 
          ? 'bg-[#13131c] text-[#F9D749]' 
          : 'text-white hover:text-[#F9D749] hover:bg-[#13131c]'
        }
      `}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex items-center justify-center w-[40px] h-[40px] min-w-[40px]">
        <div className={`w-[20px] h-[20px] transition-colors duration-200 ${isActive ? 'text-[#F9D749]' : isHovered ? 'text-[#F9D749]' : ''}`}>
          {icon}
        </div>
      </div>
      
      {isExpanded && (
        <div className="flex-1 pr-[10px] overflow-hidden whitespace-nowrap">
          <span className={`font-rubik font-medium uppercase text-[14px] transition-colors duration-200 ${isActive ? 'text-[#F9D749]' : ''}`}>
            {label}
          </span>
        </div>
      )}
    </Link>
  )
} 