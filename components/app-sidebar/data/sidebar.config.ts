import { Box, Gift, Headphones, Home, Swords, User, Users } from 'lucide-react';
import type { SidebarButtonItem } from '../sidebar-buttons';

export type SidebarNavigation = Array<{
  label: string;
  items: SidebarButtonItem[];
}>;

export const getPublicSidebarNav = (): SidebarNavigation => [
  // First group
  {
    label: 'Main',
    items: [
      { title: 'Mystery Boxes', icon: Box, href: '/cases' },
      { title: 'Battlefield', icon: Swords, href: '/battles' },
      { title: 'Rewards', icon: Gift, href: '/rewards' },
    ],
  },

  // Second group
  {
    label: 'Community',
    items: [
      { title: 'Partners', icon: User, href: '/partners' },
      { title: 'Affiliates', icon: Users, href: '/affiliates' },
      { title: 'Support', icon: Headphones, href: '/support' },
    ],
  },
];

export const getAuthSidebarNav = (): SidebarNavigation => [
  // Main navigation
  {
    label: 'Dashboard',
    items: [
      { title: 'Home', icon: Home, href: '/dashboard' },
      { title: 'Mystery Boxes', icon: Box, href: '/cases' },
      { title: 'Battlefield', icon: Swords, href: '/battles' },
      { title: 'Rewards', icon: Gift, href: '/rewards' },
    ],
  },

  // Community section
  {
    label: 'Community',
    items: [
      { title: 'Partners', icon: User, href: '/partners' },
      { title: 'Affiliates', icon: Users, href: '/affiliates' },
      { title: 'Support', icon: Headphones, href: '/support' },
    ],
  },
];
