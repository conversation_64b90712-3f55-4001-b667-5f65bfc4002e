interface UserProfileProgressProps {
  progress: number;
}

export default function UserProfileProgress({
  progress,
}: UserProfileProgressProps) {
  return (
    <div className="h-1.5 w-full overflow-hidden rounded border border-sidebar-border bg-sidebar-bg">
      <div
        className="h-full bg-blue-500 transition-all duration-300"
        style={{ width: `${Math.max(0, Math.min(progress, 1)) * 100}%` }}
      />
    </div>
  );
}
