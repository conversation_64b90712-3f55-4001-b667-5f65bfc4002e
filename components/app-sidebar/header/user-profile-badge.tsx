import { sidebarClasses } from '@rabbit/design-system/data/sidebar-classes';
import { cn } from '@rabbit/design-system/lib/utils';

function getRomanNumeral(level: number) {
  const numerals = ['I', 'II', 'III', 'IV', 'V'];
  return numerals[Math.max(0, Math.min(level - 1, 4))] || level;
}

interface UserProfileBadgeProps {
  level: number;
}

export default function UserProfileBadge({ level }: UserProfileBadgeProps) {
  return (
    <span
      className={cn(
        'inline-flex items-center justify-center rounded border border-blue-700 bg-blue-600 px-2 py-0.5 font-bold text-white text-xs shadow-md',
        sidebarClasses.profile.badge
      )}
      style={{ minWidth: 24 }}
    >
      {getRomanNumeral(level)}
    </span>
  );
}
