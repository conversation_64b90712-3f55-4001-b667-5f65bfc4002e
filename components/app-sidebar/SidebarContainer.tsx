"use client"

import { useSidebar } from './SidebarContext'

/**
 * Container component for the sidebar
 * 
 * @param props Component props
 * @param props.children React child components
 * @returns JSX.Element
 */
export default function SidebarContainer({ children }: { children: React.ReactNode }) {
  const { isExpanded } = useSidebar()

  return (
    <aside
      className={`h-screen fixed top-0 left-0 bg-[#0a0a0c] border-r border-[#242426] transition-all duration-300 z-50 flex flex-col ${
        isExpanded ? 'w-[240px]' : 'w-[60px]'
      }`}
    >
      {children}
    </aside>
  )
} 