"use client"

import { createContext, useContext, useState, useEffect } from 'react'

type SidebarContextType = {
  isExpanded: boolean
  toggleSidebar: () => void
  setExpanded: (expanded: boolean) => void
  activeId: string | null
  setActiveId: (id: string | null) => void
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined)

/**
 * Provider component for sidebar state management
 * 
 * @param props Component props
 * @param props.children React child components
 * @returns JSX.Element
 */
export function SidebarProvider({ children }: { children: React.ReactNode }) {
  const [isExpanded, setIsExpanded] = useState(true)
  const [activeId, setActiveId] = useState<string | null>(null)

  const toggleSidebar = () => {
    setIsExpanded(!isExpanded)
  }

  const setExpanded = (expanded: boolean) => {
    setIsExpanded(expanded)
  }

  // Collapse sidebar on mobile by default
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 1024) {
        setIsExpanded(false)
      }
    }

    window.addEventListener('resize', handleResize)
    handleResize()

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  return (
    <SidebarContext.Provider
      value={{
        isExpanded,
        toggleSidebar,
        setExpanded,
        activeId,
        setActiveId
      }}
    >
      {children}
    </SidebarContext.Provider>
  )
}

/**
 * Hook for accessing sidebar context
 * 
 * @returns SidebarContextType
 */
export function useSidebar(): SidebarContextType {
  const context = useContext(SidebarContext)
  if (context === undefined) {
    throw new Error('useSidebar must be used within a SidebarProvider')
  }
  return context
} 