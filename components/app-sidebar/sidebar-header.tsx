"use client"

import Link from 'next/link'
import { useSidebar } from './sidebar-context'

/**
 * Header component for the sidebar
 * 
 * @returns JSX.Element
 */
export default function SidebarHeader() {
  const { isExpanded, toggleSidebar } = useSidebar()

  return (
    <div className="flex items-center h-[60px] border-b border-[#242426] px-[10px] justify-between">
      <div className={`overflow-hidden transition-all duration-300 ${isExpanded ? 'w-auto opacity-100' : 'w-0 opacity-0'}`}>
        <Link
          href="/"
          className="font-rubik font-bold text-2xl text-[#F9D749]"
        >
          GRAPHYN
        </Link>
      </div>
        
      <button 
        type="button"
        onClick={toggleSidebar} 
        className="text-white hover:text-[#F9D749] transition-colors flex items-center justify-center w-[40px] h-[40px] min-w-[40px]"
        aria-label={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
      >
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          width="20" 
          height="20" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          aria-hidden="true"
        >
          <path d={isExpanded ? "M15 18l-6-6 6-6" : "M9 18l6-6-6-6"} />
        </svg>
      </button>
    </div>
  )
}
