"use client"

import <PERSON>bar<PERSON><PERSON>r from './sidebar-container'
import <PERSON><PERSON><PERSON>eader from './sidebar-header'
import SidebarContent from './sidebar-content'
import SidebarNav from './sidebar-nav'
import SidebarFooter from './sidebar-footer'

/**
 * Main sidebar component that assembles all sidebar parts
 * 
 * @returns JSX.Element
 */
export default function AppSidebar() {
  return (
    <SidebarContainer>
      <SidebarHeader />
      <SidebarContent>
        <SidebarNav />
      </SidebarContent>
      <SidebarFooter />
    </SidebarContainer>
  )
}
