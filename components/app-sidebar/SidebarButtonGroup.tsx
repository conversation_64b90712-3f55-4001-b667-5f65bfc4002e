"use client"

import { useSidebar } from './SidebarContext'
import SidebarBaseButton from './SidebarBaseButton'

type NavItem = {
  id: string
  name: string
  icon: React.ReactNode
  href: string
}

type SidebarButtonGroupProps = {
  title: string
  items: NavItem[]
}

/**
 * Button group component for sidebar navigation sections
 * 
 * @param props Component properties
 * @param props.title Group title
 * @param props.items Array of navigation items
 * @returns JSX.Element
 */
export default function SidebarButtonGroup({ title, items }: SidebarButtonGroupProps) {
  const { isExpanded, activeId } = useSidebar()
  
  return (
    <div className="mb-6">
      {isExpanded && (
        <div className="px-[16px] mb-2">
          <h3 className="font-rubik text-[11px] uppercase text-[#9293a0] font-medium tracking-wider">
            {title}
          </h3>
        </div>
      )}
      
      <div className="px-[10px] space-y-2">
        {items.map((item) => (
          <SidebarBaseButton
            key={item.id}
            id={item.id}
            icon={item.icon}
            label={item.name}
            href={item.href}
            isActive={item.id === activeId}
          />
        ))}
      </div>
    </div>
  )
} 