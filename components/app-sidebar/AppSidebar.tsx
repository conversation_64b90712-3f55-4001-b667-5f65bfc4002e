"use client"

import { SidebarProvider } from './SidebarContext'
import <PERSON>bar<PERSON>ontainer from './SidebarContainer'
import SidebarHeader from './SidebarHeader'
import SidebarContent from './SidebarContent'
import SidebarNav from './SidebarNav'
import SidebarFooter from './SidebarFooter'

/**
 * Main sidebar component that assembles all sidebar parts
 * 
 * @returns JSX.Element
 */
export default function AppSidebar() {
  return (
    <SidebarProvider>
      <SidebarContainer>
        <SidebarHeader />
        <SidebarContent>
          <SidebarNav />
        </SidebarContent>
        <SidebarFooter />
      </SidebarContainer>
    </SidebarProvider>
  )
} 