"use client"

import { useState } from "react"
import ForgotPasswordModal from "./ForgotPasswordModal"

/**
 * Authentication modal component with Sign In and Sign Up views
 *
 * @param props Component properties
 * @param props.isOpen Whether the modal is open
 * @param props.onClose Function to close the modal
 * @param props.initialView Initial view to show (signin or signup)
 * @returns JSX.Element
 */
export default function AuthModal({
  isOpen,
  onClose,
  initialView = "signin"
}: {
  isOpen: boolean;
  onClose: () => void;
  initialView?: "signin" | "signup";
}) {
  const [activeView, setActiveView] = useState<"signin" | "signup">(initialView);
  const [showReferralField, setShowReferralField] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 flex items-start justify-center z-50 pt-[15vh]">
        <div
          className="fixed inset-0 bg-black/50"
          onClick={onClose}
          onKeyDown={(e) => e.key === 'Escape' && onClose()}
          role="button"
          tabIndex={0}
          aria-label="Close modal"
        />
        <div className="relative bg-[#18181a] rounded-xl overflow-hidden w-[440px] max-w-full shadow-2xl border border-[#242426]">
          {/* Modal Header */}
          <div className="flex bg-[#121214] border-b border-[#242426] relative">
            <button
              type="button"
              className={`flex-1 py-4 text-center font-rubik font-medium text-[16px] transition-all duration-200 relative ${
                activeView === "signin"
                  ? "text-white border-b-2 border-[#F9D749]"
                  : "text-[#9293a0] hover:text-white hover:bg-[#1a1a1c]"
              }`}
              onClick={() => setActiveView("signin")}
            >
              Sign In
              {activeView === "signin" && (
                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-[#F9D749] to-[#fae47a]" />
              )}
            </button>
            <button
              type="button"
              className={`flex-1 py-4 text-center font-rubik font-medium text-[16px] transition-all duration-200 relative ${
                activeView === "signup"
                  ? "text-white border-b-2 border-[#F9D749]"
                  : "text-[#9293a0] hover:text-white hover:bg-[#1a1a1c]"
              }`}
              onClick={() => setActiveView("signup")}
            >
              Sign Up
              {activeView === "signup" && (
                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-[#F9D749] to-[#fae47a]" />
              )}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="absolute right-4 top-4 text-[#9293a0] hover:text-white hover:bg-[#242426] rounded-full p-1 transition-all duration-200"
              aria-label="Close modal"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true">
                <line x1="18" y1="6" x2="6" y2="18" />
                <line x1="6" y1="6" x2="18" y2="18" />
              </svg>
            </button>
          </div>

          {/* Sign In Form */}
          {activeView === "signin" && (
            <div className="p-6">
              <form onSubmit={(e) => e.preventDefault()}>
                {/* OAuth Buttons */}
                <div className="grid grid-cols-2 gap-3 mb-6">
                  <button
                    type="button"
                    className="flex items-center justify-center py-3 px-4 bg-[#0a0a0c] text-[#f5f5f5] font-rubik rounded-lg hover:bg-[#13131c] transition-all duration-200 border border-[#242426] hover:border-[#3a3a3c] group"
                    aria-label="Sign in with Google"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="#4285F4" className="mr-2 group-hover:scale-110 transition-transform duration-200" aria-hidden="true">
                      <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                      <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853" />
                      <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05" />
                      <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335" />
                    </svg>
                    <span className="font-medium">Google</span>
                  </button>
                  <button
                    type="button"
                    className="flex items-center justify-center py-3 px-4 bg-[#0a0a0c] text-[#f5f5f5] font-rubik rounded-lg hover:bg-[#13131c] transition-all duration-200 border border-[#242426] hover:border-[#3a3a3c] group"
                    aria-label="Sign in with Discord"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="#5865F2" className="mr-2 group-hover:scale-110 transition-transform duration-200" aria-hidden="true">
                      <path d="M20.317 4.492c-1.53-.69-3.17-1.2-4.885-1.49a.075.075 0 0 0-.079.036c-.21.39-.444.975-.608 1.41a18.27 18.27 0 0 0-5.487 0 12.278 12.278 0 0 0-.617-1.41.077.077 0 0 0-.079-.036c-1.714.29-3.354.8-4.885 1.49a.07.07 0 0 0-.032.028C.533 9.093-.32 13.555.099 17.961a.08.08 0 0 0 .031.055 20.03 20.03 0 0 0 6.04 3.056.077.077 0 0 0 .084-.028c.462-.63.874-1.295 1.226-1.994a.076.076 0 0 0-.041-.106 13.229 13.229 0 0 1-1.872-.892.077.077 0 0 1-.008-.128c.126-.094.252-.192.372-.292a.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.1.246.198.373.292a.077.077 0 0 1-.006.129 12.217 12.217 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.963 19.963 0 0 0 6.04-3.056.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.278c-1.182 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z" />
                    </svg>
                    <span className="font-medium">Discord</span>
                  </button>
                </div>

                {/* Divider with OR CONTINUE text */}
                <div className="flex items-center mb-4">
                  <div className="flex-grow border-t border-[#242426]" />
                  <div className="mx-4 px-3 py-1 border border-[#242426] rounded text-[#d0d0da] text-sm font-rubik">
                    OR CONTINUE WITH
                  </div>
                  <div className="flex-grow border-t border-[#242426]" />
                </div>

                <div className="mb-5">
                  <label htmlFor="signin-email" className="block text-[#d0d0da] text-sm font-rubik mb-2 uppercase font-medium tracking-wide">
                    EMAIL OR USERNAME
                  </label>
                  <input
                    id="signin-email"
                    type="text"
                    className="w-full px-4 py-3 bg-[#0a0a0c] text-white font-rubik rounded-lg focus:outline-none focus:ring-2 focus:ring-[#F9D749] border border-[#242426] hover:border-[#3a3a3c] transition-all duration-200"
                    placeholder="Enter email or username"
                  />
                </div>

                <div className="mb-6">
                  <div className="flex justify-between mb-2">
                    <label htmlFor="signin-password" className="block text-[#d0d0da] text-sm font-rubik uppercase font-medium tracking-wide">
                      PASSWORD
                    </label>
                    <button
                      type="button"
                      className="text-sm text-[#F9D749] hover:text-[#fae47a] font-rubik transition-colors duration-200"
                      onClick={() => setShowForgotPassword(true)}
                    >
                      Forgot password?
                    </button>
                  </div>
                  <input
                    id="signin-password"
                    type="password"
                    className="w-full px-4 py-3 bg-[#0a0a0c] text-white font-rubik rounded-lg focus:outline-none focus:ring-2 focus:ring-[#F9D749] border border-[#242426] hover:border-[#3a3a3c] transition-all duration-200"
                    placeholder="••••••••"
                  />
                </div>

                <div className="mt-6">
                  <button
                    type="submit"
                    className="w-full py-3 bg-gradient-to-r from-[#F9D749] to-[#fae47a] text-[#171923] font-rubik font-semibold rounded-lg hover:from-[#fae47a] hover:to-[#F9D749] transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] shadow-lg"
                  >
                    Sign In
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Sign Up Form */}
          {activeView === "signup" && (
            <div className="p-6">
              <form onSubmit={(e) => e.preventDefault()}>
                {/* Required Form Fields */}
                <div className="mb-5">
                  <label htmlFor="signup-username" className="block text-[#d0d0da] text-sm font-rubik mb-2 uppercase font-medium tracking-wide">
                    USERNAME
                  </label>
                  <input
                    id="signup-username"
                    type="text"
                    className="w-full px-4 py-3 bg-[#0a0a0c] text-white font-rubik rounded-lg focus:outline-none focus:ring-2 focus:ring-[#F9D749] border border-[#242426] hover:border-[#3a3a3c] transition-all duration-200"
                    placeholder="Enter username"
                  />
                </div>

                <div className="mb-5">
                  <label htmlFor="signup-email" className="block text-[#d0d0da] text-sm font-rubik mb-2 uppercase font-medium tracking-wide">
                    EMAIL
                  </label>
                  <input
                    id="signup-email"
                    type="email"
                    className="w-full px-4 py-3 bg-[#0a0a0c] text-white font-rubik rounded-lg focus:outline-none focus:ring-2 focus:ring-[#F9D749] border border-[#242426] hover:border-[#3a3a3c] transition-all duration-200"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="mb-5">
                  <label htmlFor="signup-password" className="block text-[#d0d0da] text-sm font-rubik mb-2 uppercase font-medium tracking-wide">
                    PASSWORD
                  </label>
                  <input
                    id="signup-password"
                    type="password"
                    className="w-full px-4 py-3 bg-[#0a0a0c] text-white font-rubik rounded-lg focus:outline-none focus:ring-2 focus:ring-[#F9D749] border border-[#242426] hover:border-[#3a3a3c] transition-all duration-200"
                    placeholder="••••••••"
                  />
                </div>

                {/* Referral Code Checkbox */}
                <div className="mb-3">
                  <div className="flex items-center w-full">
                    <input
                      id="referral-checkbox"
                      type="checkbox"
                      className="w-4 h-4 border-[#242426] rounded focus:ring-[#F9D749] text-[#F9D749]"
                      checked={showReferralField}
                      onChange={(e) => setShowReferralField(e.target.checked)}
                    />
                    <label htmlFor="referral-checkbox" className="ml-2 text-[#d0d0da] text-sm font-rubik uppercase font-medium">
                      I HAVE A REFERRAL CODE (OPTIONAL)
                    </label>
                  </div>

                  {showReferralField && (
                    <div className="mt-2">
                      <input
                        id="referral-code"
                        type="text"
                        className="w-full px-4 py-2 bg-[#0a0a0c] text-[#d0d0da] font-rubik rounded-md focus:outline-none focus:ring-1 focus:ring-[#F9D749]"
                        placeholder="Enter referral code"
                      />
                    </div>
                  )}
                </div>

                {/* Terms of Service Agreement */}
                <div className="mb-3">
                  <div className="flex items-center w-full">
                    <input
                      id="terms-checkbox"
                      type="checkbox"
                      className="w-4 h-4 border-[#242426] rounded focus:ring-[#F9D749] text-[#F9D749]"
                      required
                    />
                    <label htmlFor="terms-checkbox" className="ml-2 text-[#d0d0da] text-sm font-rubik uppercase font-medium">
                      I AGREE TO THE <button type="button" className="text-[#F9D749] hover:underline uppercase">TERMS OF SERVICE</button>
                    </label>
                  </div>
                </div>

                {/* Submit Button */}
                <div className="mt-6">
                  <button
                    type="submit"
                    className="w-full py-3 bg-gradient-to-r from-[#F9D749] to-[#fae47a] text-[#171923] font-rubik font-semibold rounded-lg hover:from-[#fae47a] hover:to-[#F9D749] transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] shadow-lg"
                  >
                    CREATE ACCOUNT
                  </button>
                </div>
              </form>
            </div>
          )}
        </div>
      </div>

      {/* Forgot Password Modal */}
      <ForgotPasswordModal
        isOpen={showForgotPassword}
        onClose={() => setShowForgotPassword(false)}
      />
    </>
  );
}