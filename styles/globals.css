@import url('https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
    --radius: 0.5rem;
    --sidebar: oklch(0.98 0.00 247.84);
    --sidebar-background: oklch(0.98 0.00 247.84);
    --sidebar-foreground: oklch(0.27 0 0);
    --sidebar-primary: oklch(0.77 0.16 70.08);
    --sidebar-primary-foreground: oklch(1.00 0 0);
    --sidebar-accent: oklch(0.99 0.02 95.28);
    --sidebar-accent-foreground: oklch(0.47 0.12 46.20);
    --sidebar-border: oklch(0.93 0.01 264.53);
    --sidebar-ring: oklch(0.77 0.16 70.08);
    --font-sans: Inter, sans-serif;
    --font-serif: Source Serif 4, serif;
    --font-mono: JetBrains Mono, monospace;
    --shadow-2xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
    --shadow: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
    --shadow-md: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10);
    --shadow-lg: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10);
    --shadow-xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10);
    --shadow-2xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.25);
  }
  
  .dark {
    --background: oklch(0.20 0 0);
    --foreground: oklch(0.92 0 0);
    --card: oklch(0.27 0 0);
    --card-foreground: oklch(0.92 0 0);
    --popover: oklch(0.27 0 0);
    --popover-foreground: oklch(0.92 0 0);
    --primary: oklch(0.77 0.16 70.08);
    --primary-foreground: oklch(0 0 0);
    --secondary: oklch(0.27 0 0);
    --secondary-foreground: oklch(0.92 0 0);
    --muted: oklch(0.27 0 0);
    --muted-foreground: oklch(0.72 0 0);
    --accent: oklch(0.47 0.12 46.20);
    --accent-foreground: oklch(0.92 0.12 95.75);
    --destructive: oklch(0.64 0.21 25.33);
    --destructive-foreground: oklch(1.00 0 0);
    --border: oklch(0.37 0 0);
    --input: oklch(0.37 0 0);
    --ring: oklch(0.77 0.16 70.08);
    --chart-1: oklch(0.84 0.16 84.43);
    --chart-2: oklch(0.67 0.16 58.32);
    --chart-3: oklch(0.47 0.12 46.20);
    --chart-4: oklch(0.56 0.15 49.00);
    --chart-5: oklch(0.47 0.12 46.20);
    --radius: 0.375rem;
    --sidebar: oklch(0.17 0 0);
    --sidebar-background: oklch(0.17 0 0);
    --sidebar-foreground: oklch(0.92 0 0);
    --sidebar-primary: oklch(0.77 0.16 70.08);
    --sidebar-primary-foreground: oklch(1.00 0 0);
    --sidebar-accent: oklch(0.47 0.12 46.20);
    --sidebar-accent-foreground: oklch(0.92 0.12 95.75);
    --sidebar-border: oklch(0.37 0 0);
    --sidebar-ring: oklch(0.77 0.16 70.08);
    --font-sans: Inter, sans-serif;
    --font-serif: Source Serif 4, serif;
    --font-mono: JetBrains Mono, monospace;
    --shadow-2xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
    --shadow: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
    --shadow-md: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10);
    --shadow-lg: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10);
    --shadow-xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10);
    --shadow-2xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.25);
  }
  
  .theme {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);

    --font-sans: var(--font-sans);
    --font-mono: var(--font-mono);
    --font-serif: var(--font-serif);

    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);

    --shadow-2xs: var(--shadow-2xs);
    --shadow-xs: var(--shadow-xs);
    --shadow-sm: var(--shadow-sm);
    --shadow: var(--shadow);
    --shadow-md: var(--shadow-md);
    --shadow-lg: var(--shadow-lg);
    --shadow-xl: var(--shadow-xl);
    --shadow-2xl: var(--shadow-2xl);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Add direct font-family fallback for Rubik */
.font-rubik {
  font-family: 'Rubik', ui-sans-serif, system-ui, sans-serif;
}
